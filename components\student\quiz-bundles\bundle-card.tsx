'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  ShoppingCart, 
  CheckCircle,
  Play,
  Trophy,
  Target,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { RazorpayCheckout } from '@/components/payments/razorpay-checkout'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'

interface Quiz {
  id: string
  title: string
  type: string
  difficulty: string
  timeLimit?: number
}

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  quizCount: number
  quizzes: Quiz[]
  purchaseCount: number
}

interface BundleCardProps {
  bundle: Bundle
  isPurchased?: boolean
  progress?: {
    totalQuizzes: number
    completedQuizzes: number
    progressPercentage: number
  }
  onPurchaseSuccess?: () => void
}

export function BundleCard({ bundle, isPurchased, progress, onPurchaseSuccess }: BundleCardProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [purchasing, setPurchasing] = useState(false)

  const handlePurchase = async () => {
    if (!session) {
      toast.error('Please sign in to purchase bundles')
      router.push('/auth/signin')
      return
    }

    if (bundle.price === 0) {
      // Handle free bundle
      try {
        setPurchasing(true)
        const response = await fetch(`/api/quiz-bundles/${bundle.id}/purchase`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ paymentMethod: 'free' })
        })

        if (response.ok) {
          const data = await response.json()
          toast.success('Bundle purchased successfully!')
          if (onPurchaseSuccess) onPurchaseSuccess()
          router.push(data.data.redirectUrl || `/student/quiz-bundles/${bundle.slug}`)
        } else {
          const error = await response.json()
          toast.error(error.message || 'Failed to purchase bundle')
        }
      } catch (error) {
        console.error('Purchase error:', error)
        toast.error('Failed to purchase bundle')
      } finally {
        setPurchasing(false)
      }
    } else {
      // Handle paid bundle
      setShowPaymentModal(true)
    }
  }

  const handlePaymentSuccess = () => {
    toast.success('Payment successful! Welcome to the bundle!')
    if (onPurchaseSuccess) onPurchaseSuccess()
    setShowPaymentModal(false)
  }

  const handleViewBundle = () => {
    if (isPurchased) {
      router.push(`/student/quiz-bundles/${bundle.slug}`)
    } else {
      router.push(`/quiz-bundles/${bundle.slug}`)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelIcon = (level?: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return <Target className="h-4 w-4" />
      case 'intermediate': return <Zap className="h-4 w-4" />
      case 'advanced': return <Trophy className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        whileHover={{ y: -5 }}
        className="h-full"
      >
        <Card className="h-full flex flex-col bg-white/95 backdrop-blur-xl border-0 shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-4">
            {/* Bundle Image */}
            <div className="relative h-48 rounded-lg overflow-hidden mb-4">
              {bundle.thumbnailImage ? (
                <img
                  src={bundle.thumbnailImage}
                  alt={bundle.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center">
                  <BookOpen className="h-16 w-16 text-white" />
                </div>
              )}
              
              {/* Price Badge */}
              <div className="absolute top-3 right-3">
                {bundle.price === 0 ? (
                  <Badge className="bg-green-500 text-white">Free</Badge>
                ) : (
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                    <div className="flex items-center gap-1">
                      <span className="text-lg font-bold text-gray-900">₹{bundle.price}</span>
                      {bundle.originalPrice && bundle.originalPrice > bundle.price && (
                        <span className="text-sm text-gray-500 line-through">₹{bundle.originalPrice}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Purchase Status */}
              {isPurchased && (
                <div className="absolute top-3 left-3">
                  <Badge className="bg-green-500 text-white flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Purchased
                  </Badge>
                </div>
              )}
            </div>

            {/* Bundle Info */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {bundle.level && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      {getLevelIcon(bundle.level)}
                      {bundle.level}
                    </Badge>
                  )}
                  {bundle.category && (
                    <Badge variant="secondary">{bundle.category}</Badge>
                  )}
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Users className="h-4 w-4" />
                  {bundle.purchaseCount}
                </div>
              </div>

              <h3 className="text-xl font-bold text-gray-900 line-clamp-2">
                {bundle.title}
              </h3>

              {bundle.shortDescription && (
                <p className="text-gray-600 text-sm line-clamp-2">
                  {bundle.shortDescription}
                </p>
              )}
            </div>
          </CardHeader>

          <CardContent className="flex-1 space-y-4">
            {/* Progress (if purchased) */}
            {isPurchased && progress && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium">{progress.progressPercentage}%</span>
                </div>
                <Progress value={progress.progressPercentage} className="h-2" />
                <div className="text-xs text-gray-500">
                  {progress.completedQuizzes} of {progress.totalQuizzes} quizzes completed
                </div>
              </div>
            )}

            {/* Bundle Stats */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <BookOpen className="h-4 w-4" />
                <span>{bundle.quizCount} Quizzes</span>
              </div>
              {bundle.duration && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span>{bundle.duration}</span>
                </div>
              )}
            </div>

            {/* Quiz Types */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Quiz Types:</p>
              <div className="flex flex-wrap gap-1">
                {Array.from(new Set(bundle.quizzes.map(q => q.type))).map(type => (
                  <Badge key={type} variant="outline" className="text-xs">
                    {type}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Features */}
            {bundle.features.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">Features:</p>
                <ul className="text-xs text-gray-600 space-y-1">
                  {bundle.features.slice(0, 3).map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      {feature}
                    </li>
                  ))}
                  {bundle.features.length > 3 && (
                    <li className="text-gray-500">+{bundle.features.length - 3} more</li>
                  )}
                </ul>
              </div>
            )}

            {/* Tags */}
            {bundle.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {bundle.tags.slice(0, 3).map(tag => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
                {bundle.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{bundle.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </CardContent>

          <CardFooter className="pt-4">
            <div className="w-full space-y-2">
              {isPurchased ? (
                <Button 
                  onClick={handleViewBundle}
                  className="w-full"
                  size="lg"
                >
                  <Play className="mr-2 h-4 w-4" />
                  Continue Learning
                </Button>
              ) : (
                <>
                  <Button 
                    onClick={handlePurchase}
                    disabled={purchasing}
                    className="w-full"
                    size="lg"
                  >
                    {purchasing ? (
                      'Processing...'
                    ) : bundle.price === 0 ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Get Free Bundle
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        Purchase for ₹{bundle.price}
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={handleViewBundle}
                    className="w-full"
                  >
                    View Details
                  </Button>
                </>
              )}
            </div>
          </CardFooter>
        </Card>
      </motion.div>

      {/* Payment Modal */}
      {session && (
        <RazorpayCheckout
          course={{
            id: bundle.id,
            title: bundle.title,
            price: bundle.price,
            instructor: {
              name: 'PrepLocus'
            }
          }}
          user={{
            name: session.user.name || '',
            email: session.user.email || ''
          }}
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </>
  )
}
