import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProgressSchema = z.object({
  lessonId: z.string().min(1, 'Lesson ID is required'),
  watchTime: z.number().min(0, 'Watch time must be non-negative').optional(),
  lastPosition: z.number().min(0, 'Last position must be non-negative').optional(),
  isCompleted: z.boolean().optional()
})

const bulkUpdateProgressSchema = z.object({
  updates: z.array(updateProgressSchema)
})

// GET /api/student/courses/progress - Get user's course progress
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const url = new URL(request.url)
      const courseId = url.searchParams.get('courseId')
      const lessonId = url.searchParams.get('lessonId')

      if (lessonId) {
        // Get progress for specific lesson
        const progress = await prisma.courseProgress.findUnique({
          where: {
            userId_lessonId: {
              userId: user.id,
              lessonId
            }
          },
          include: {
            lesson: {
              select: {
                id: true,
                title: true,
                duration: true,
                chapter: {
                  select: {
                    id: true,
                    title: true,
                    section: {
                      select: {
                        id: true,
                        title: true,
                        course: {
                          select: {
                            id: true,
                            title: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        })

        return APIResponse.success({
          progress: progress || {
            userId: user.id,
            lessonId,
            isCompleted: false,
            watchTime: 0,
            lastPosition: 0,
            firstAccessAt: null,
            lastAccessAt: null,
            completedAt: null
          }
        })
      }

      if (courseId) {
        // Get progress for entire course
        const courseProgress = await prisma.courseProgress.findMany({
          where: {
            userId: user.id,
            lesson: {
              chapter: {
                section: {
                  courseId
                }
              }
            }
          },
          include: {
            lesson: {
              select: {
                id: true,
                title: true,
                duration: true,
                order: true,
                chapter: {
                  select: {
                    id: true,
                    title: true,
                    order: true,
                    section: {
                      select: {
                        id: true,
                        title: true,
                        order: true
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: [
            { lesson: { chapter: { section: { order: 'asc' } } } },
            { lesson: { chapter: { order: 'asc' } } },
            { lesson: { order: 'asc' } }
          ]
        })

        // Calculate course completion statistics
        const totalLessons = await prisma.courseLesson.count({
          where: {
            chapter: {
              section: {
                courseId,
                isPublished: true
              },
              isPublished: true
            },
            isPublished: true
          }
        })

        const completedLessons = courseProgress.filter(p => p.isCompleted).length
        const totalWatchTime = courseProgress.reduce((acc, p) => acc + p.watchTime, 0)
        const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

        return APIResponse.success({
          courseId,
          progress: courseProgress,
          statistics: {
            totalLessons,
            completedLessons,
            totalWatchTime,
            completionPercentage,
            lastAccessedAt: courseProgress.length > 0 
              ? Math.max(...courseProgress.map(p => new Date(p.lastAccessAt).getTime()))
              : null
          }
        })
      }

      // Get progress for all enrolled courses
      const enrollments = await prisma.courseEnrollment.findMany({
        where: { userId: user.id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              slug: true
            }
          }
        }
      })

      const progressSummary = await Promise.all(
        enrollments.map(async (enrollment) => {
          const courseProgress = await prisma.courseProgress.findMany({
            where: {
              userId: user.id,
              lesson: {
                chapter: {
                  section: {
                    courseId: enrollment.courseId
                  }
                }
              }
            }
          })

          const totalLessons = await prisma.courseLesson.count({
            where: {
              chapter: {
                section: {
                  courseId: enrollment.courseId,
                  isPublished: true
                },
                isPublished: true
              },
              isPublished: true
            }
          })

          const completedLessons = courseProgress.filter(p => p.isCompleted).length
          const totalWatchTime = courseProgress.reduce((acc, p) => acc + p.watchTime, 0)
          const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

          return {
            course: enrollment.course,
            enrollment: {
              id: enrollment.id,
              enrolledAt: enrollment.enrolledAt,
              status: enrollment.status
            },
            progress: {
              totalLessons,
              completedLessons,
              totalWatchTime,
              completionPercentage,
              lastAccessedAt: courseProgress.length > 0 
                ? Math.max(...courseProgress.map(p => new Date(p.lastAccessAt).getTime()))
                : null
            }
          }
        })
      )

      return APIResponse.success({
        courses: progressSummary
      })
    } catch (error) {
      console.error('Error fetching course progress:', error)
      return APIResponse.error('Failed to fetch course progress', 500)
    }
  }
)

// POST /api/student/courses/progress - Update lesson progress
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: updateProgressSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { lessonId, watchTime, lastPosition, isCompleted } = validatedBody

      console.log(`Progress update attempt: User ${user.id}, Lesson ${lessonId}`)

      // Verify user is enrolled in the course containing this lesson
      const lesson = await prisma.courseLesson.findUnique({
        where: { id: lessonId },
        include: {
          chapter: {
            include: {
              section: {
                include: {
                  course: {
                    include: {
                      enrollments: {
                        where: { userId: user.id },
                        select: { id: true, status: true }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!lesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      const enrollment = lesson.chapter.section.course.enrollments[0]
      console.log(`Enrollment check: Course ${lesson.chapter.section.course.id}, Enrollment:`, enrollment)

      if (!enrollment) {
        return APIResponse.error('You are not enrolled in this course. Please enroll first to track progress.', 403)
      }

      if (enrollment.status !== 'active') {
        return APIResponse.error(`Course enrollment is ${enrollment.status}. Only active enrollments can track progress.`, 403)
      }

      // Update or create progress record
      const now = new Date()
      const progressData: any = {
        lastAccessAt: now
      }

      if (watchTime !== undefined) progressData.watchTime = watchTime
      if (lastPosition !== undefined) progressData.lastPosition = lastPosition
      if (isCompleted !== undefined) {
        progressData.isCompleted = isCompleted
        if (isCompleted) {
          progressData.completedAt = now
        }
      }

      const progress = await prisma.courseProgress.upsert({
        where: {
          userId_lessonId: {
            userId: user.id,
            lessonId
          }
        },
        update: progressData,
        create: {
          userId: user.id,
          lessonId,
          ...progressData,
          firstAccessAt: now
        }
      })

      // Update course enrollment progress
      await updateCourseEnrollmentProgress(user.id, lesson.chapter.section.course.id)

      return APIResponse.success({
        message: 'Progress updated successfully',
        progress
      })
    } catch (error) {
      console.error('Error updating course progress:', error)
      return APIResponse.error('Failed to update course progress', 500)
    }
  }
)

// Helper function to update overall course enrollment progress
async function updateCourseEnrollmentProgress(userId: string, courseId: string) {
  try {
    // Get all lessons in the course
    const totalLessons = await prisma.courseLesson.count({
      where: {
        chapter: {
          section: {
            courseId,
            isPublished: true
          },
          isPublished: true
        },
        isPublished: true
      }
    })

    // Get completed lessons
    const completedLessons = await prisma.courseProgress.count({
      where: {
        userId,
        isCompleted: true,
        lesson: {
          chapter: {
            section: {
              courseId
            }
          }
        }
      }
    })

    // Calculate progress percentage
    const progressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

    // Update enrollment record
    const updateData: any = {
      progress: progressPercentage,
      lastAccessedAt: new Date()
    }

    // Mark as completed if 100% progress
    if (progressPercentage === 100) {
      updateData.status = 'completed'
      updateData.completedAt = new Date()
    }

    await prisma.courseEnrollment.updateMany({
      where: {
        userId,
        courseId
      },
      data: updateData
    })
  } catch (error) {
    console.error('Error updating course enrollment progress:', error)
  }
}
