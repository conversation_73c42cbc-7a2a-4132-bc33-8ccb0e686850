import { prisma } from '@/lib/prisma'
import { razorpayService } from '@/lib/razorpay-service'

export interface CreateBundleParams {
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags?: string[]
  features?: string[]
  quizIds: string[]
}

export interface UpdateBundleParams extends Partial<CreateBundleParams> {
  id: string
  isActive?: boolean
  isPublished?: boolean
}

export interface BundleProgress {
  totalQuizzes: number
  completedQuizzes: number
  progressPercentage: number
  lastAccessedAt?: Date
}

class QuizBundleService {
  /**
   * Create a new quiz bundle
   */
  async createBundle(params: CreateBundleParams) {
    try {
      const {
        title,
        description,
        shortDescription,
        price,
        originalPrice,
        slug,
        thumbnailImage,
        category,
        level,
        duration,
        tags = [],
        features = [],
        quizIds
      } = params

      // Check if slug is unique
      const existingBundle = await prisma.quizBundle.findUnique({
        where: { slug }
      })

      if (existingBundle) {
        throw new Error('Bundle slug already exists')
      }

      // Verify all quizzes exist and are published
      const quizzes = await prisma.quiz.findMany({
        where: {
          id: { in: quizIds },
          isPublished: true
        }
      })

      if (quizzes.length !== quizIds.length) {
        throw new Error('Some quizzes not found or not published')
      }

      // Create bundle with quiz items
      const bundle = await prisma.quizBundle.create({
        data: {
          title,
          description,
          shortDescription,
          price,
          originalPrice,
          slug,
          thumbnailImage,
          category,
          level,
          duration,
          tags,
          features,
          items: {
            create: quizIds.map((quizId, index) => ({
              quizId,
              order: index + 1,
              isRequired: true
            }))
          }
        },
        include: {
          items: {
            include: {
              quiz: {
                select: {
                  id: true,
                  title: true,
                  type: true,
                  difficulty: true,
                  timeLimit: true
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        }
      })

      return bundle
    } catch (error) {
      console.error('Error creating quiz bundle:', error)
      throw error
    }
  }

  /**
   * Update an existing quiz bundle
   */
  async updateBundle(params: UpdateBundleParams) {
    try {
      const { id, quizIds, ...updateData } = params

      // Check if bundle exists
      const existingBundle = await prisma.quizBundle.findUnique({
        where: { id }
      })

      if (!existingBundle) {
        throw new Error('Bundle not found')
      }

      // If slug is being updated, check uniqueness
      if (updateData.slug && updateData.slug !== existingBundle.slug) {
        const slugExists = await prisma.quizBundle.findUnique({
          where: { slug: updateData.slug }
        })

        if (slugExists) {
          throw new Error('Bundle slug already exists')
        }
      }

      // Update bundle
      const updatedBundle = await prisma.$transaction(async (tx) => {
        // Update bundle data
        const bundle = await tx.quizBundle.update({
          where: { id },
          data: updateData
        })

        // If quizIds are provided, update bundle items
        if (quizIds) {
          // Remove existing items
          await tx.quizBundleItem.deleteMany({
            where: { bundleId: id }
          })

          // Add new items
          if (quizIds.length > 0) {
            await tx.quizBundleItem.createMany({
              data: quizIds.map((quizId, index) => ({
                bundleId: id,
                quizId,
                order: index + 1,
                isRequired: true
              }))
            })
          }
        }

        return bundle
      })

      return updatedBundle
    } catch (error) {
      console.error('Error updating quiz bundle:', error)
      throw error
    }
  }

  /**
   * Get bundle by ID with full details
   */
  async getBundleById(id: string, includeQuizzes = true) {
    try {
      const bundle = await prisma.quizBundle.findUnique({
        where: { id },
        include: {
          items: includeQuizzes ? {
            include: {
              quiz: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  type: true,
                  difficulty: true,
                  timeLimit: true,
                  estimatedTime: true,
                  points: true,
                  category: true,
                  tags: true,
                  isPublished: true
                }
              }
            },
            orderBy: { order: 'asc' }
          } : true,
          purchases: {
            select: {
              id: true,
              userId: true,
              status: true,
              progress: true
            }
          }
        }
      })

      return bundle
    } catch (error) {
      console.error('Error fetching bundle:', error)
      throw error
    }
  }

  /**
   * Get bundle by slug
   */
  async getBundleBySlug(slug: string) {
    try {
      const bundle = await prisma.quizBundle.findUnique({
        where: { slug },
        include: {
          items: {
            include: {
              quiz: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  type: true,
                  difficulty: true,
                  timeLimit: true,
                  estimatedTime: true,
                  points: true,
                  category: true,
                  tags: true
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        }
      })

      return bundle
    } catch (error) {
      console.error('Error fetching bundle by slug:', error)
      throw error
    }
  }

  /**
   * Get all published bundles for students
   */
  async getPublishedBundles(filters?: {
    category?: string
    level?: string
    priceRange?: { min: number; max: number }
    search?: string
  }) {
    try {
      const where: any = {
        isPublished: true,
        isActive: true
      }

      if (filters?.category) {
        where.category = filters.category
      }

      if (filters?.level) {
        where.level = filters.level
      }

      if (filters?.priceRange) {
        where.price = {
          gte: filters.priceRange.min,
          lte: filters.priceRange.max
        }
      }

      if (filters?.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { tags: { has: filters.search } }
        ]
      }

      const bundles = await prisma.quizBundle.findMany({
        where,
        include: {
          items: {
            include: {
              quiz: {
                select: {
                  id: true,
                  title: true,
                  type: true,
                  difficulty: true,
                  timeLimit: true
                }
              }
            },
            orderBy: { order: 'asc' }
          },
          _count: {
            select: {
              purchases: {
                where: { status: 'active' }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      return bundles
    } catch (error) {
      console.error('Error fetching published bundles:', error)
      throw error
    }
  }

  /**
   * Purchase a quiz bundle
   */
  async purchaseBundle(userId: string, bundleId: string, paymentId?: string) {
    try {
      // Check if bundle exists and is available
      const bundle = await prisma.quizBundle.findUnique({
        where: { id: bundleId }
      })

      if (!bundle || !bundle.isPublished || !bundle.isActive) {
        throw new Error('Bundle not available for purchase')
      }

      // Check if user already purchased this bundle
      const existingPurchase = await prisma.quizBundlePurchase.findUnique({
        where: {
          userId_bundleId: {
            userId,
            bundleId
          }
        }
      })

      if (existingPurchase && existingPurchase.status === 'active') {
        throw new Error('Bundle already purchased')
      }

      // Create or update purchase
      const purchase = await prisma.quizBundlePurchase.upsert({
        where: {
          userId_bundleId: {
            userId,
            bundleId
          }
        },
        update: {
          status: 'active',
          paymentId,
          purchasedAt: new Date(),
          progress: 0
        },
        create: {
          userId,
          bundleId,
          paymentId,
          status: 'active',
          progress: 0
        }
      })

      return purchase
    } catch (error) {
      console.error('Error purchasing bundle:', error)
      throw error
    }
  }

  /**
   * Get user's purchased bundles
   */
  async getUserBundles(userId: string, status?: string) {
    try {
      const where: any = { userId }
      
      if (status && status !== 'all') {
        where.status = status
      }

      const purchases = await prisma.quizBundlePurchase.findMany({
        where,
        include: {
          bundle: {
            include: {
              items: {
                include: {
                  quiz: {
                    select: {
                      id: true,
                      title: true,
                      type: true,
                      difficulty: true,
                      timeLimit: true
                    }
                  }
                },
                orderBy: { order: 'asc' }
              }
            }
          }
        },
        orderBy: { purchasedAt: 'desc' }
      })

      return purchases
    } catch (error) {
      console.error('Error fetching user bundles:', error)
      throw error
    }
  }

  /**
   * Calculate bundle progress for a user
   */
  async calculateBundleProgress(userId: string, bundleId: string): Promise<BundleProgress> {
    try {
      // Get bundle with quizzes
      const bundle = await prisma.quizBundle.findUnique({
        where: { id: bundleId },
        include: {
          items: {
            include: {
              quiz: { select: { id: true } }
            }
          }
        }
      })

      if (!bundle) {
        throw new Error('Bundle not found')
      }

      const quizIds = bundle.items.map(item => item.quiz.id)
      const totalQuizzes = quizIds.length

      // Get completed quiz attempts
      const completedAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId,
          quizId: { in: quizIds },
          isCompleted: true
        },
        select: { quizId: true },
        distinct: ['quizId']
      })

      const completedQuizzes = completedAttempts.length
      const progressPercentage = totalQuizzes > 0 ? (completedQuizzes / totalQuizzes) * 100 : 0

      // Get last access time
      const lastAccess = await prisma.quizAttempt.findFirst({
        where: {
          userId,
          quizId: { in: quizIds }
        },
        orderBy: { startedAt: 'desc' },
        select: { startedAt: true }
      })

      return {
        totalQuizzes,
        completedQuizzes,
        progressPercentage: Math.round(progressPercentage),
        lastAccessedAt: lastAccess?.startedAt
      }
    } catch (error) {
      console.error('Error calculating bundle progress:', error)
      throw error
    }
  }

  /**
   * Check if user has access to a quiz through bundle purchase
   */
  async hasQuizAccess(userId: string, quizId: string): Promise<boolean> {
    try {
      // Check if quiz is in any purchased bundle
      const bundleAccess = await prisma.quizBundlePurchase.findFirst({
        where: {
          userId,
          status: 'active',
          bundle: {
            items: {
              some: {
                quizId
              }
            }
          }
        }
      })

      return !!bundleAccess
    } catch (error) {
      console.error('Error checking quiz access:', error)
      return false
    }
  }

  /**
   * Delete a quiz bundle
   */
  async deleteBundle(id: string) {
    try {
      // Check if bundle has any purchases
      const purchaseCount = await prisma.quizBundlePurchase.count({
        where: { bundleId: id }
      })

      if (purchaseCount > 0) {
        throw new Error('Cannot delete bundle with existing purchases')
      }

      // Delete bundle (cascade will handle items)
      await prisma.quizBundle.delete({
        where: { id }
      })

      return true
    } catch (error) {
      console.error('Error deleting bundle:', error)
      throw error
    }
  }
}

// Export singleton instance
export const quizBundleService = new QuizBundleService()
