'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Loader2, CreditCard, Shield, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

declare global {
  interface Window {
    Razorpay: any
  }
}

interface Course {
  id: string
  title: string
  price: number
  instructor: {
    name: string
  }
}

interface User {
  name: string
  email: string
}

interface RazorpayCheckoutProps {
  course: Course
  user: User
  isOpen: boolean
  onClose: () => void
  onSuccess?: (paymentId: string, enrollmentId: string) => void
}

export function RazorpayCheckout({ course, user, isOpen, onClose, onSuccess }: RazorpayCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isScriptLoaded, setIsScriptLoaded] = useState(false)
  const router = useRouter()

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        if (window.Razorpay) {
          setIsScriptLoaded(true)
          resolve(true)
          return
        }

        const script = document.createElement('script')
        script.src = 'https://checkout.razorpay.com/v1/checkout.js'
        script.onload = () => {
          setIsScriptLoaded(true)
          resolve(true)
        }
        script.onerror = () => {
          console.error('Failed to load Razorpay script')
          resolve(false)
        }
        document.body.appendChild(script)
      })
    }

    if (isOpen) {
      loadRazorpayScript()
    }
  }, [isOpen])

  const handlePayment = async () => {
    if (!isScriptLoaded) {
      toast.error('Payment system is loading. Please try again.')
      return
    }

    setIsLoading(true)

    try {
      // Create Razorpay order
      const orderResponse = await fetch('/api/payments/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          courseId: course.id,
          amount: course.price,
          currency: 'INR'
        })
      })

      if (!orderResponse.ok) {
        const error = await orderResponse.json()
        throw new Error(error.message || 'Failed to create payment order')
      }

      const orderData = await orderResponse.json()
      const { orderId, amount, currency, keyId } = orderData.data

      // Configure Razorpay options
      const options = {
        key: keyId,
        amount: amount,
        currency: currency,
        name: 'PrepLocus',
        description: `Enrollment for ${course.title}`,
        order_id: orderId,
        prefill: {
          name: user.name,
          email: user.email
        },
        theme: {
          color: '#2563eb'
        },
        modal: {
          ondismiss: () => {
            setIsLoading(false)
            toast.info('Payment cancelled')
          }
        },
        handler: async (response: any) => {
          try {
            // Verify payment
            const verifyResponse = await fetch('/api/payments/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature
              })
            })

            if (!verifyResponse.ok) {
              const error = await verifyResponse.json()
              throw new Error(error.message || 'Payment verification failed')
            }

            const verifyData = await verifyResponse.json()
            
            toast.success('Payment successful! Welcome to the course!')
            onClose()
            
            if (onSuccess) {
              onSuccess(verifyData.data.paymentId, verifyData.data.enrollmentId)
            }

            // Redirect to course
            if (verifyData.data.redirectUrl) {
              router.push(verifyData.data.redirectUrl)
            }

          } catch (error: any) {
            console.error('Payment verification error:', error)
            toast.error(error.message || 'Payment verification failed')
          } finally {
            setIsLoading(false)
          }
        }
      }

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options)
      
      razorpay.on('payment.failed', async (response: any) => {
        try {
          // Report payment failure
          await fetch('/api/payments/failure', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              razorpay_order_id: orderId,
              error: response.error
            })
          })
        } catch (error) {
          console.error('Error reporting payment failure:', error)
        }

        toast.error(`Payment failed: ${response.error.description}`)
        setIsLoading(false)
      })

      razorpay.open()

    } catch (error: any) {
      console.error('Payment error:', error)
      toast.error(error.message || 'Failed to initiate payment')
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Complete Your Purchase
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Course Details */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border">
            <h3 className="font-semibold text-gray-900 mb-2">{course.title}</h3>
            <p className="text-sm text-gray-600 mb-3">by {course.instructor.name}</p>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-blue-600">₹{course.price}</span>
              <Badge variant="secondary">Lifetime Access</Badge>
            </div>
          </div>

          {/* Security Features */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Secure Payment</span>
            </div>
            <ul className="text-xs text-green-700 space-y-1">
              <li className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                256-bit SSL encryption
              </li>
              <li className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                PCI DSS compliant
              </li>
              <li className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                Powered by Razorpay
              </li>
            </ul>
          </div>

          {/* Payment Button */}
          <Button
            onClick={handlePayment}
            disabled={isLoading || !isScriptLoaded}
            className="w-full h-12 text-lg"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : !isScriptLoaded ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading Payment System...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Pay ₹{course.price}
              </>
            )}
          </Button>

          <p className="text-xs text-gray-500 text-center">
            By proceeding, you agree to our Terms of Service and Privacy Policy.
            You will get lifetime access to this course.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
