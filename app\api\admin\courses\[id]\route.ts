import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCourseSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be non-negative').optional(),
  originalPrice: z.number().min(0).optional(),
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  language: z.string().optional(),
  duration: z.string().optional(),
  thumbnailImage: z.string().url().optional(),
  features: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  requirements: z.array(z.string()).optional(),
  whatYouLearn: z.array(z.string()).optional(),
  isPublished: z.boolean().optional(),
  isActive: z.boolean().optional()
})

// GET /api/admin/courses/[id] - Get course details for admin
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          sections: {
            orderBy: { updatedAt: 'asc' },
            include: {
              chapters: {
                orderBy: { updatedAt: 'asc' },
                include: {
                  lessons: {
                    orderBy: { updatedAt: 'asc' },
                    include: {
                      video: true
                    }
                  }
                }
              }
            }
          },
          quizzes: {
            orderBy: { createdAt: 'asc' },
            include: {
              questions: true,
              _count: {
                select: { attempts: true }
              }
            }
          },
          _count: {
            select: { 
              enrollments: true,
              discussions: true,
              certificates: true
            }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Calculate course statistics
      const totalLessons = course.sections.reduce((acc, section) => 
        acc + section.chapters.reduce((chapterAcc, chapter) => 
          chapterAcc + chapter.lessons.length, 0), 0)
      
      const totalDuration = course.sections.reduce((acc, section) => 
        acc + section.chapters.reduce((chapterAcc, chapter) => 
          chapterAcc + chapter.lessons.reduce((lessonAcc, lesson) => 
            lessonAcc + (lesson.duration || 0), 0), 0), 0)

      const courseWithStats = {
        ...course,
        totalLessons,
        totalDuration: Math.round(totalDuration / 60), // Convert to minutes
        totalQuizzes: course.quizzes.length,
        totalQuestions: course.quizzes.reduce((acc, quiz) => acc + quiz.questions.length, 0)
      }

      return APIResponse.success({ course: courseWithStats })
    } catch (error) {
      console.error('Error fetching course:', error)
      return APIResponse.error('Failed to fetch course', 500)
    }
  }
)

// PUT /api/admin/courses/[id] - Update course
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateCourseSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if course exists
      const existingCourse = await prisma.course.findUnique({
        where: { id: courseId }
      })

      if (!existingCourse) {
        return APIResponse.error('Course not found', 404)
      }

      const updateData = { ...validatedBody }

      // If title is being updated, regenerate slug
      if (updateData.title && updateData.title !== existingCourse.title) {
        const baseSlug = updateData.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()

        // Ensure slug is unique
        let slug = baseSlug
        let counter = 1
        while (await prisma.course.findFirst({ 
          where: { 
            slug,
            id: { not: courseId } // Exclude current course
          } 
        })) {
          slug = `${baseSlug}-${counter}`
          counter++
        }
        updateData.slug = slug
      }

      // Handle publishing
      if (updateData.isPublished !== undefined) {
        if (updateData.isPublished && !existingCourse.isPublished) {
          updateData.publishedAt = new Date()
        } else if (!updateData.isPublished && existingCourse.isPublished) {
          updateData.publishedAt = null
        }
      }

      // Update the course
      const updatedCourse = await prisma.course.update({
        where: { id: courseId },
        data: updateData,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Course updated successfully',
        course: updatedCourse
      })
    } catch (error) {
      console.error('Error updating course:', error)
      return APIResponse.error('Failed to update course', 500)
    }
  }
)

// DELETE /api/admin/courses/[id] - Delete course
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          _count: {
            select: { enrollments: true }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if course has enrollments
      if (course._count.enrollments > 0) {
        // Soft delete - mark as inactive instead of hard delete
        await prisma.course.update({
          where: { id: courseId },
          data: { 
            isActive: false,
            isPublished: false
          }
        })

        return APIResponse.success({
          message: 'Course deactivated successfully (has enrollments)',
          softDelete: true
        })
      } else {
        // Hard delete if no enrollments
        await prisma.course.delete({
          where: { id: courseId }
        })

        return APIResponse.success({
          message: 'Course deleted successfully',
          softDelete: false
        })
      }
    } catch (error) {
      console.error('Error deleting course:', error)
      return APIResponse.error('Failed to delete course', 500)
    }
  }
)
