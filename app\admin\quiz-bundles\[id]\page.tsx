'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Users, 
  BookOpen, 
  DollarSign, 
  Calendar,
  Eye,
  EyeOff,
  Tag,
  Clock,
  Trophy,
  Target,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { BundleForm } from '@/components/admin/quiz-bundles/bundle-form'
import { toast } from 'sonner'

interface Quiz {
  id: string
  title: string
  description?: string
  type: string
  difficulty: string
  timeLimit?: number
  order: number
  isRequired: boolean
}

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  isActive: boolean
  isPublished: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  quizzes: Quiz[]
  stats: {
    totalPurchases: number
    activePurchases: number
    revenue: number
  }
}

export default function BundleViewPage() {
  const params = useParams()
  const router = useRouter()
  const bundleId = params?.id as string

  const [bundle, setBundle] = useState<Bundle | null>(null)
  const [loading, setLoading] = useState(true)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    if (bundleId) {
      fetchBundle()
    }
  }, [bundleId])

  const fetchBundle = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/quiz-bundles/${bundleId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Bundle not found')
          router.push('/admin/quiz-bundles')
          return
        }
        throw new Error('Failed to fetch bundle')
      }

      const data = await response.json()
      setBundle(data.data || data)
    } catch (error) {
      console.error('Error fetching bundle:', error)
      toast.error('Failed to load bundle details')
      router.push('/admin/quiz-bundles')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = async (bundleData: any) => {
    try {
      setUpdating(true)
      const response = await fetch(`/api/quiz-bundles/${bundleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bundleData)
      })

      if (response.ok) {
        toast.success('Bundle updated successfully!')
        setShowEditModal(false)
        fetchBundle() // Refresh data
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update bundle')
      }
    } catch (error) {
      console.error('Error updating bundle:', error)
      toast.error('Failed to update bundle')
    } finally {
      setUpdating(false)
    }
  }

  const handleDelete = async () => {
    try {
      setDeleting(true)
      const response = await fetch(`/api/quiz-bundles/${bundleId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Bundle deleted successfully!')
        router.push('/admin/quiz-bundles')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to delete bundle')
      }
    } catch (error) {
      console.error('Error deleting bundle:', error)
      toast.error('Failed to delete bundle')
    } finally {
      setDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelIcon = (level?: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return <Target className="h-4 w-4" />
      case 'intermediate': return <Zap className="h-4 w-4" />
      case 'advanced': return <Trophy className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!bundle) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bundle not found</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              The quiz bundle you're looking for doesn't exist.
            </p>
            <Button onClick={() => router.push('/admin/quiz-bundles')} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Bundles
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/admin/quiz-bundles')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Bundles
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                {bundle.title}
              </h1>
              <div className="flex items-center gap-2 mt-2">
                {bundle.isPublished ? (
                  <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    Published
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <EyeOff className="h-3 w-3" />
                    Draft
                  </Badge>
                )}
                {!bundle.isActive && (
                  <Badge variant="destructive">Inactive</Badge>
                )}
                {bundle.level && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    {getLevelIcon(bundle.level)}
                    {bundle.level}
                  </Badge>
                )}
                {bundle.category && (
                  <Badge variant="secondary">{bundle.category}</Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setShowEditModal(true)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(true)}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </Button>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Price</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {bundle.price === 0 ? 'Free' : `₹${bundle.price}`}
                  </p>
                  {bundle.originalPrice && bundle.originalPrice > bundle.price && (
                    <p className="text-sm text-gray-500 line-through">₹{bundle.originalPrice}</p>
                  )}
                </div>
                <DollarSign className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Quizzes</p>
                  <p className="text-2xl font-bold text-green-600">{bundle.quizzes.length}</p>
                </div>
                <BookOpen className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Purchases</p>
                  <p className="text-2xl font-bold text-purple-600">{bundle.stats?.totalPurchases || 0}</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Revenue</p>
                  <p className="text-2xl font-bold text-orange-600">₹{bundle.stats?.revenue || 0}</p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="quizzes">Quizzes ({bundle.quizzes.length})</TabsTrigger>
            <TabsTrigger value="purchases">Purchases</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {bundle.description || bundle.shortDescription || 'No description provided'}
                    </p>
                  </CardContent>
                </Card>

                {bundle.features.length > 0 && (
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle>Features</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {bundle.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div className="space-y-6">
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>Bundle Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Slug:</span>
                      <span className="font-mono text-sm">{bundle.slug}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created:</span>
                      <span>{new Date(bundle.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Updated:</span>
                      <span>{new Date(bundle.updatedAt).toLocaleDateString()}</span>
                    </div>
                    {bundle.publishedAt && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Published:</span>
                        <span>{new Date(bundle.publishedAt).toLocaleDateString()}</span>
                      </div>
                    )}
                    {bundle.duration && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration:</span>
                        <span>{bundle.duration}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {bundle.tags.length > 0 && (
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle>Tags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {bundle.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Quizzes Tab */}
          <TabsContent value="quizzes" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {bundle.quizzes.map((quiz, index) => (
                <Card key={quiz.id} className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <Badge variant="outline" className="text-xs">
                        Quiz {quiz.order}
                      </Badge>
                      <Badge className={getDifficultyColor(quiz.difficulty)}>
                        {quiz.difficulty}
                      </Badge>
                    </div>
                    
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {quiz.title}
                    </h3>
                    
                    {quiz.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                        {quiz.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{quiz.type}</span>
                      {quiz.timeLimit && (
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {quiz.timeLimit}m
                        </span>
                      )}
                      {quiz.isRequired && (
                        <Badge variant="outline" className="text-xs">Required</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Purchases Tab */}
          <TabsContent value="purchases" className="space-y-6">
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                  Purchase Analytics
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Detailed purchase analytics and user management coming soon.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Bundle Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <h4 className="font-medium">Published</h4>
                      <p className="text-sm text-gray-600">Visible to students</p>
                    </div>
                    <Badge className={bundle.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {bundle.isPublished ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <h4 className="font-medium">Active</h4>
                      <p className="text-sm text-gray-600">Available for purchase</p>
                    </div>
                    <Badge className={bundle.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                      {bundle.isActive ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Edit Modal */}
        <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Quiz Bundle</DialogTitle>
            </DialogHeader>
            <BundleForm
              bundle={bundle}
              onSubmit={handleEdit}
              onCancel={() => setShowEditModal(false)}
              isLoading={updating}
            />
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Quiz Bundle</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-gray-600">
                Are you sure you want to delete "{bundle.title}"? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteDialog(false)}
                  disabled={deleting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? 'Deleting...' : 'Delete Bundle'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
