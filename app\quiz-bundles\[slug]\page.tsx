'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Award, 
  CheckCircle,
  ShoppingCart,
  ArrowLeft,
  Target,
  Trophy,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RazorpayCheckout } from '@/components/payments/razorpay-checkout'
import { toast } from 'sonner'
import { useSession } from 'next-auth/react'

interface Quiz {
  id: string
  title: string
  description?: string
  type: string
  difficulty: string
  timeLimit?: number
  estimatedTime?: number
  points?: number
  category?: string
  tags: string[]
  order: number
  isRequired: boolean
  isPublished: boolean
}

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  price: number
  originalPrice?: number
  isActive: boolean
  isPublished: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  quizzes: Quiz[]
  stats: {
    totalQuizzes: number
    totalPurchases: number
    activePurchases: number
  }
}

export default function BundleDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session } = useSession()
  const bundleSlug = params?.slug as string

  const [bundle, setBundle] = useState<Bundle | null>(null)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'curriculum' | 'reviews'>('overview')

  useEffect(() => {
    if (bundleSlug) {
      fetchBundleDetails()
    }
  }, [bundleSlug])

  const fetchBundleDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/quiz-bundles?slug=${bundleSlug}`)
      
      if (!response.ok) {
        throw new Error('Bundle not found')
      }

      const data = await response.json()
      console.log('Bundle details API response:', data)
      // Quiz bundles API returns { data: { bundles: [...] } }
      const bundles = data.data?.bundles || []
      const foundBundle = bundles.find((b: Bundle) => b.slug === bundleSlug)
      
      if (!foundBundle) {
        throw new Error('Bundle not found')
      }

      setBundle(foundBundle)
    } catch (error) {
      console.error('Error fetching bundle details:', error)
      toast.error('Bundle not found')
      router.push('/quiz-bundles')
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async () => {
    if (!session) {
      toast.error('Please sign in to purchase bundles')
      router.push('/auth/signin')
      return
    }

    if (!bundle) return

    if (bundle.price === 0) {
      // Handle free bundle
      try {
        setPurchasing(true)
        const response = await fetch(`/api/quiz-bundles/${bundle.id}/purchase`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ paymentMethod: 'free' })
        })

        if (response.ok) {
          const data = await response.json()
          toast.success('Bundle purchased successfully!')
          router.push(data.data.redirectUrl || `/student/quiz-bundles/${bundle.slug}`)
        } else {
          const error = await response.json()
          toast.error(error.message || 'Failed to purchase bundle')
        }
      } catch (error) {
        console.error('Purchase error:', error)
        toast.error('Failed to purchase bundle')
      } finally {
        setPurchasing(false)
      }
    } else {
      // Handle paid bundle
      setShowPaymentModal(true)
    }
  }

  const handlePaymentSuccess = () => {
    toast.success('Payment successful! Welcome to the bundle!')
    setShowPaymentModal(false)
    router.push(`/student/quiz-bundles/${bundle?.slug}`)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelIcon = (level?: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return <Target className="h-4 w-4" />
      case 'intermediate': return <Zap className="h-4 w-4" />
      case 'advanced': return <Trophy className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!bundle) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bundle not found</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              The quiz bundle you're looking for doesn't exist.
            </p>
            <Button onClick={() => router.push('/quiz-bundles')} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Bundles
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => router.push('/quiz-bundles')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bundles
          </Button>
        </motion.div>

        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8"
        >
          {/* Bundle Info */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                {bundle.level && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    {getLevelIcon(bundle.level)}
                    {bundle.level}
                  </Badge>
                )}
                {bundle.category && (
                  <Badge variant="secondary">{bundle.category}</Badge>
                )}
              </div>
              
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent mb-4">
                {bundle.title}
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                {bundle.description || bundle.shortDescription}
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{bundle.stats.totalQuizzes}</div>
                <div className="text-sm text-gray-500">Quizzes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{bundle.stats.activePurchases}</div>
                <div className="text-sm text-gray-500">Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {bundle.duration || 'Self-paced'}
                </div>
                <div className="text-sm text-gray-500">Duration</div>
              </div>
            </div>

            {/* Tags */}
            {bundle.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {bundle.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Purchase Card */}
          <div className="lg:col-span-1">
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl sticky top-8">
              <CardContent className="p-6">
                {/* Bundle Image */}
                <div className="relative h-48 rounded-lg overflow-hidden mb-6">
                  {bundle.thumbnailImage ? (
                    <img
                      src={bundle.thumbnailImage}
                      alt={bundle.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center">
                      <BookOpen className="h-16 w-16 text-white" />
                    </div>
                  )}
                </div>

                {/* Pricing */}
                <div className="text-center mb-6">
                  {bundle.price === 0 ? (
                    <div className="text-3xl font-bold text-green-600">Free</div>
                  ) : (
                    <div>
                      <div className="text-3xl font-bold text-gray-900 dark:text-white">
                        ₹{bundle.price}
                      </div>
                      {bundle.originalPrice && bundle.originalPrice > bundle.price && (
                        <div className="text-lg text-gray-500 line-through">
                          ₹{bundle.originalPrice}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Purchase Button */}
                <Button
                  onClick={handlePurchase}
                  disabled={purchasing}
                  className="w-full h-12 text-lg mb-4"
                  size="lg"
                >
                  {purchasing ? (
                    'Processing...'
                  ) : bundle.price === 0 ? (
                    <>
                      <CheckCircle className="mr-2 h-5 w-5" />
                      Get Free Bundle
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      Purchase Bundle
                    </>
                  )}
                </Button>

                {/* Features */}
                {bundle.features.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white">What's included:</h4>
                    <ul className="space-y-1">
                      {bundle.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>About This Bundle</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-gray dark:prose-invert max-w-none">
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {bundle.description || "This comprehensive quiz bundle is designed to help you master key concepts through interactive practice. Each quiz is carefully crafted to build upon previous knowledge and reinforce learning through repetition and varied question types."}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Curriculum Tab */}
          <TabsContent value="curriculum" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-4"
            >
              {bundle.quizzes.map((quiz, index) => (
                <Card key={quiz.id} className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-400 font-semibold">
                            {quiz.order}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {quiz.title}
                          </h3>
                          {quiz.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {quiz.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 mt-2">
                            <Badge className={getDifficultyColor(quiz.difficulty)}>
                              {quiz.difficulty}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <BookOpen className="h-4 w-4" />
                              {quiz.type}
                            </div>
                            {quiz.timeLimit && (
                              <div className="flex items-center gap-1 text-sm text-gray-500">
                                <Clock className="h-4 w-4" />
                                {quiz.timeLimit}m
                              </div>
                            )}
                            {quiz.points && (
                              <div className="flex items-center gap-1 text-sm text-gray-500">
                                <Trophy className="h-4 w-4" />
                                {quiz.points} pts
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      {quiz.isRequired && (
                        <Badge variant="outline">Required</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          </TabsContent>

          {/* Reviews Tab */}
          <TabsContent value="reviews" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                <CardContent className="p-12 text-center">
                  <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                    No reviews yet
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Be the first to purchase this bundle and leave a review!
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>
        </Tabs>

        {/* Payment Modal */}
        {session && bundle && (
          <RazorpayCheckout
            course={{
              id: bundle.id,
              title: bundle.title,
              price: bundle.price,
              instructor: {
                name: 'PrepLocus'
              }
            }}
            user={{
              name: session.user.name || '',
              email: session.user.email || ''
            }}
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            onSuccess={handlePaymentSuccess}
          />
        )}
      </div>
    </div>
  )
}
