const { createServer } = require('http')
const { Server } = require('socket.io')
const { PrismaClient } = require('../lib/generated/prisma')

// Initialize Prisma client
const prisma = new PrismaClient()

// Get port from Railway environment or default
const port = process.env.PORT || 3001
const isDev = process.env.NODE_ENV !== 'production'

// Store connected users and active rooms
const connectedUsers = new Map()
const activeRooms = new Map()

// Create HTTP server
const httpServer = createServer((req, res) => {
  // Health check endpoint for Railway
  if (req.url === '/health' || req.url === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'socket-server',
      port: port,
      connectedUsers: connectedUsers.size,
      activeRooms: activeRooms.size
    }))
    return
  }
  
  // Default response
  res.writeHead(200, { 'Content-Type': 'text/plain' })
  res.end('PrepLocus Socket Server is running')
})

// Initialize Socket.IO server
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXTAUTH_URL || (isDev ? "http://localhost:3000" : "*"),
    methods: ["GET", "POST"],
    credentials: true
  },
  path: '/socket.io',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000
})

// Socket connection handling
io.on('connection', (socket) => {
  console.log('🔌 New socket connection:', socket.id)

  // Handle user authentication
  socket.on('authenticate', async (data) => {
    try {
      const { userId, userRole, userName } = data
      
      if (userId) {
        connectedUsers.set(socket.id, {
          userId,
          userRole,
          userName,
          connectedAt: new Date()
        })
        
        socket.userId = userId
        socket.userRole = userRole
        socket.userName = userName
        
        // Join user-specific room
        socket.join(`user:${userId}`)
        
        // Join role-specific room
        if (userRole) {
          socket.join(`role:${userRole}`)
        }
        
        console.log(`✅ User authenticated: ${userName} (${userId}) - ${userRole}`)
        
        socket.emit('authenticated', {
          success: true,
          message: 'Successfully authenticated'
        })
      }
    } catch (error) {
      console.error('Authentication error:', error)
      socket.emit('authentication_error', {
        success: false,
        message: 'Authentication failed'
      })
    }
  })

  // Handle joining specific rooms (courses, quizzes, etc.)
  socket.on('join_room', (data) => {
    try {
      const { roomId, roomType } = data
      const roomKey = `${roomType}:${roomId}`
      
      socket.join(roomKey)
      
      // Track active rooms
      if (!activeRooms.has(roomKey)) {
        activeRooms.set(roomKey, new Set())
      }
      activeRooms.get(roomKey).add(socket.id)
      
      console.log(`📍 User ${socket.userName || socket.id} joined room: ${roomKey}`)
      
      socket.emit('room_joined', {
        roomId,
        roomType,
        success: true
      })
      
      // Notify others in the room
      socket.to(roomKey).emit('user_joined_room', {
        userId: socket.userId,
        userName: socket.userName,
        roomId,
        roomType
      })
    } catch (error) {
      console.error('Error joining room:', error)
      socket.emit('room_error', {
        success: false,
        message: 'Failed to join room'
      })
    }
  })

  // Handle leaving rooms
  socket.on('leave_room', (data) => {
    try {
      const { roomId, roomType } = data
      const roomKey = `${roomType}:${roomId}`
      
      socket.leave(roomKey)
      
      // Update active rooms tracking
      if (activeRooms.has(roomKey)) {
        activeRooms.get(roomKey).delete(socket.id)
        if (activeRooms.get(roomKey).size === 0) {
          activeRooms.delete(roomKey)
        }
      }
      
      console.log(`📤 User ${socket.userName || socket.id} left room: ${roomKey}`)
      
      // Notify others in the room
      socket.to(roomKey).emit('user_left_room', {
        userId: socket.userId,
        userName: socket.userName,
        roomId,
        roomType
      })
    } catch (error) {
      console.error('Error leaving room:', error)
    }
  })

  // Handle real-time notifications
  socket.on('send_notification', async (data) => {
    try {
      if (socket.userRole !== 'ADMIN') {
        socket.emit('error', { message: 'Unauthorized to send notifications' })
        return
      }

      const { targetUsers, notification } = data
      
      // Send to specific users or broadcast
      if (targetUsers && targetUsers.length > 0) {
        targetUsers.forEach(userId => {
          io.to(`user:${userId}`).emit('notification', notification)
        })
      } else {
        // Broadcast to all connected users
        io.emit('notification', notification)
      }
      
      console.log(`📢 Notification sent by ${socket.userName}:`, notification.title)
    } catch (error) {
      console.error('Error sending notification:', error)
      socket.emit('error', { message: 'Failed to send notification' })
    }
  })

  // Handle live quiz events
  socket.on('quiz_event', (data) => {
    try {
      const { quizId, eventType, eventData } = data
      const roomKey = `quiz:${quizId}`
      
      // Broadcast quiz event to all participants
      socket.to(roomKey).emit('quiz_event', {
        eventType,
        eventData,
        timestamp: new Date().toISOString(),
        fromUser: {
          id: socket.userId,
          name: socket.userName
        }
      })
      
      console.log(`🎯 Quiz event (${eventType}) in quiz ${quizId} by ${socket.userName}`)
    } catch (error) {
      console.error('Error handling quiz event:', error)
    }
  })

  // Handle course progress updates
  socket.on('progress_update', async (data) => {
    try {
      const { courseId, lessonId, progress } = data
      
      if (!socket.userId) {
        socket.emit('error', { message: 'User not authenticated' })
        return
      }
      
      // Update progress in database
      await prisma.courseProgress.upsert({
        where: {
          userId_lessonId: {
            userId: socket.userId,
            lessonId: lessonId
          }
        },
        update: {
          watchTime: progress.watchTime || 0,
          lastPosition: progress.lastPosition || 0,
          isCompleted: progress.isCompleted || false,
          lastAccessAt: new Date()
        },
        create: {
          userId: socket.userId,
          lessonId: lessonId,
          watchTime: progress.watchTime || 0,
          lastPosition: progress.lastPosition || 0,
          isCompleted: progress.isCompleted || false,
          firstAccessAt: new Date(),
          lastAccessAt: new Date()
        }
      })
      
      // Notify course room about progress update
      const courseRoomKey = `course:${courseId}`
      socket.to(courseRoomKey).emit('progress_updated', {
        userId: socket.userId,
        userName: socket.userName,
        lessonId,
        progress
      })
      
      console.log(`📈 Progress updated for user ${socket.userName} in lesson ${lessonId}`)
    } catch (error) {
      console.error('Error updating progress:', error)
      socket.emit('error', { message: 'Failed to update progress' })
    }
  })

  // Handle typing indicators for discussions
  socket.on('typing_start', (data) => {
    const { roomId, roomType } = data
    const roomKey = `${roomType}:${roomId}`
    
    socket.to(roomKey).emit('user_typing', {
      userId: socket.userId,
      userName: socket.userName
    })
  })

  socket.on('typing_stop', (data) => {
    const { roomId, roomType } = data
    const roomKey = `${roomType}:${roomId}`
    
    socket.to(roomKey).emit('user_stopped_typing', {
      userId: socket.userId,
      userName: socket.userName
    })
  })

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`🔌 Socket disconnected: ${socket.id} (${socket.userName || 'Unknown'}) - Reason: ${reason}`)
    
    // Clean up user data
    connectedUsers.delete(socket.id)
    
    // Clean up room tracking
    activeRooms.forEach((users, roomKey) => {
      if (users.has(socket.id)) {
        users.delete(socket.id)
        if (users.size === 0) {
          activeRooms.delete(roomKey)
        } else {
          // Notify others in the room
          socket.to(roomKey).emit('user_disconnected', {
            userId: socket.userId,
            userName: socket.userName
          })
        }
      }
    })
  })

  // Handle connection errors
  socket.on('error', (error) => {
    console.error('Socket error:', error)
  })
})

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...')
  
  // Close all socket connections
  io.close(() => {
    console.log('✅ Socket.IO server closed')
    
    // Close database connection
    prisma.$disconnect().then(() => {
      console.log('✅ Database connection closed')
      process.exit(0)
    })
  })
})

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...')
  
  io.close(() => {
    console.log('✅ Socket.IO server closed')
    
    prisma.$disconnect().then(() => {
      console.log('✅ Database connection closed')
      process.exit(0)
    })
  })
})

// Start the server
httpServer.listen(port, () => {
  console.log(`🚀 Socket server running on port ${port}`)
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`🔗 CORS origin: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}`)
})

// Log server statistics every 5 minutes
setInterval(() => {
  console.log(`📊 Server stats - Connected users: ${connectedUsers.size}, Active rooms: ${activeRooms.size}`)
}, 5 * 60 * 1000)
