import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  category: z.string().optional(),
  level: z.string().optional(),
  status: z.enum(['published', 'draft', 'all']).optional().default('all'),
  instructorId: z.string().optional()
})

const createCourseSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be non-negative').default(0),
  originalPrice: z.number().min(0).optional(),
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  language: z.string().default('en'),
  duration: z.string().optional(),
  thumbnailImage: z.string().url().optional(),
  features: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  whatYouLearn: z.array(z.string()).default([]),
  isPublished: z.boolean().default(false)
})

// GET /api/admin/courses - Get all courses for admin
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { 
        page = 1, 
        limit = 20, 
        search, 
        category,
        level,
        status,
        instructorId 
      } = validatedQuery

      // Build where clause for filtering
      const where: any = {
        isActive: true
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { shortDescription: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (category) {
        // Handle both category name and category ID
        where.OR = [
          { category: { contains: category, mode: 'insensitive' } },
          { categoryId: category }
        ]
      }

      if (level) {
        where.level = { equals: level, mode: 'insensitive' }
      }

      if (status !== 'all') {
        where.isPublished = status === 'published'
      }

      if (instructorId) {
        where.instructorId = instructorId
      }

      // Get total count for pagination
      const total = await prisma.course.count({ where })

      // Get courses with pagination
      const courses = await prisma.course.findMany({
        where,
        orderBy: [
          { isPublished: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          sections: {
            orderBy: { updatedAt: 'asc' },
            include: {
              chapters: {
                orderBy: { updatedAt: 'asc' },
                include: {
                  lessons: {
                    orderBy: { updatedAt: 'asc' },
                    select: { id: true, duration: true }
                  }
                }
              }
            }
          },
          _count: {
            select: { 
              enrollments: true,
              sections: true,
              quizzes: true,
              discussions: true
            }
          }
        }
      })

      // Add calculated fields to courses
      const coursesWithStats = courses.map(course => {
        const totalLessons = course.sections.reduce((acc, section) => 
          acc + section.chapters.reduce((chapterAcc, chapter) => 
            chapterAcc + chapter.lessons.length, 0), 0)
        
        const totalDuration = course.sections.reduce((acc, section) => 
          acc + section.chapters.reduce((chapterAcc, chapter) => 
            chapterAcc + chapter.lessons.reduce((lessonAcc, lesson) => 
              lessonAcc + (lesson.duration || 0), 0), 0), 0)

        return {
          ...course,
          totalLessons,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          sections: undefined // Remove sections from response to keep it clean
        }
      })

      return APIResponse.success({
        courses: coursesWithStats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } catch (error) {
      console.error('Error fetching admin courses:', error)
      return APIResponse.error('Failed to fetch courses', 500)
    }
  }
)

// POST /api/admin/courses - Create a new course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createCourseSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseData = validatedBody

      // Generate slug from title
      const baseSlug = courseData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()

      // Ensure slug is unique
      let slug = baseSlug
      let counter = 1
      while (await prisma.course.findUnique({ where: { slug } })) {
        slug = `${baseSlug}-${counter}`
        counter++
      }

      // Create the course
      const course = await prisma.course.create({
        data: {
          ...courseData,
          slug,
          instructorId: user.id, // Admin creates course as instructor
          publishedAt: courseData.isPublished ? new Date() : null
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Course created successfully',
        course
      })
    } catch (error) {
      console.error('Error creating course:', error)
      return APIResponse.error('Failed to create course', 500)
    }
  }
)
